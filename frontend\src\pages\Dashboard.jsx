import React, { useState, useEffect } from 'react'
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Box,
  Alert
} from '@mui/material'
import {
  AccountCircle,
  Description,
  AttachMoney,
  Payment
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import Layout from '../components/Layout'
import axios from 'axios'

const Dashboard = () => {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [documents, setDocuments] = useState(null)
  const [loans, setLoans] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      // Fetch documents
      try {
        const docsResponse = await axios.get('/api/documents/my-documents')
        setDocuments(docsResponse.data.document)
      } catch (error) {
        if (error.response?.status !== 404) {
          console.error('Error fetching documents:', error)
        }
      }

      // Fetch loans
      try {
        const loansResponse = await axios.get('/api/loans/my-loans')
        setLoans(loansResponse.data.loans)
      } catch (error) {
        console.error('Error fetching loans:', error)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved':
        return 'success'
      case 'pending':
        return 'warning'
      case 'rejected':
        return 'error'
      default:
        return 'default'
    }
  }

  const getNextStep = () => {
    if (!documents) {
      return {
        title: 'Upload Documents',
        description: 'Upload your Aadhar card, PAN card, photo, and bank details',
        action: () => navigate('/documents'),
        buttonText: 'Upload Documents'
      }
    }

    if (documents.status === 'pending') {
      return {
        title: 'Verification Pending',
        description: 'Your documents are under review. Please wait for approval.',
        action: null,
        buttonText: null
      }
    }

    if (documents.status === 'approved' && loans.length === 0) {
      return {
        title: 'Apply for Loan',
        description: 'Your documents are approved. You can now apply for a loan.',
        action: () => navigate('/loan-application'),
        buttonText: 'Apply for Loan'
      }
    }

    const pendingLoan = loans.find(loan => 
      ['pending', 'payment_required', 'payment_submitted'].includes(loan.status)
    )

    if (pendingLoan) {
      if (pendingLoan.status === 'payment_required') {
        return {
          title: 'Payment Required',
          description: `Pay ₹${pendingLoan.processingFee} processing fee to proceed with your loan application.`,
          action: () => navigate(`/payment/${pendingLoan._id}`),
          buttonText: 'Make Payment'
        }
      }

      if (pendingLoan.status === 'payment_submitted') {
        return {
          title: 'Payment Under Review',
          description: 'Your payment is being verified. Please wait for confirmation.',
          action: () => navigate(`/payment-status/${pendingLoan._id}`),
          buttonText: 'Check Status'
        }
      }
    }

    return {
      title: 'Apply for New Loan',
      description: 'You can apply for a new loan.',
      action: () => navigate('/loan-application'),
      buttonText: 'Apply for New Loan'
    }
  }

  if (loading) {
    return (
      <Layout title="Dashboard">
        <Typography>Loading...</Typography>
      </Layout>
    )
  }

  const nextStep = getNextStep()

  return (
    <Layout title="Dashboard">
      <Grid container spacing={3}>
        {/* Welcome Card */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <AccountCircle sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography variant="h5">
                    Welcome, {user.name}!
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Mobile: {user.mobile} | Email: {user.email}
                  </Typography>
                </Box>
              </Box>
              <Chip
                label={user.verificationStatus}
                color={getStatusColor(user.verificationStatus)}
                size="small"
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Next Step Card */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Next Step
              </Typography>
              <Typography variant="body1" paragraph>
                {nextStep.title}
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                {nextStep.description}
              </Typography>
              {nextStep.action && (
                <Button
                  variant="contained"
                  onClick={nextStep.action}
                  fullWidth
                >
                  {nextStep.buttonText}
                </Button>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Document Status Card */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Description sx={{ mr: 2 }} />
                <Typography variant="h6">
                  Document Status
                </Typography>
              </Box>
              {documents ? (
                <>
                  <Chip
                    label={documents.status}
                    color={getStatusColor(documents.status)}
                    sx={{ mb: 2 }}
                  />
                  <Typography variant="body2" color="text.secondary">
                    Uploaded on: {new Date(documents.uploadedAt).toLocaleDateString()}
                  </Typography>
                  {documents.rejectionReason && (
                    <Alert severity="error" sx={{ mt: 2 }}>
                      {documents.rejectionReason}
                    </Alert>
                  )}
                </>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No documents uploaded yet
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Loans Overview */}
        {loans.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <AttachMoney sx={{ mr: 2 }} />
                  <Typography variant="h6">
                    Your Loans
                  </Typography>
                </Box>
                <Grid container spacing={2}>
                  {loans.map((loan) => (
                    <Grid item xs={12} md={6} key={loan._id}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="h6">
                            ₹{loan.loanAmount.toLocaleString()}
                          </Typography>
                          <Chip
                            label={loan.status.replace('_', ' ')}
                            color={getStatusColor(loan.status)}
                            size="small"
                            sx={{ mb: 1 }}
                          />
                          <Typography variant="body2" color="text.secondary">
                            Applied: {new Date(loan.applicationDate).toLocaleDateString()}
                          </Typography>
                          {loan.status === 'payment_required' && (
                            <Button
                              size="small"
                              variant="outlined"
                              onClick={() => navigate(`/payment/${loan._id}`)}
                              sx={{ mt: 1 }}
                            >
                              Pay ₹{loan.processingFee}
                            </Button>
                          )}
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </Layout>
  )
}

export default Dashboard
