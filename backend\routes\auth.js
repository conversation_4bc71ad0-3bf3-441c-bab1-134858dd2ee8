const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const auth = require('../middleware/auth');
const fileStorage = require('../utils/fileStorage');

const router = express.Router();

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, { expiresIn: '7d' });
};

// Register user
router.post('/register', async (req, res) => {
  try {
    const { name, mobile, email, password } = req.body;

    // Use file storage if MongoDB is not available
    if (req.app.locals.useFileStorage) {
      const user = await fileStorage.createUser({ name, mobile, email, password });
      const token = generateToken(user._id);

      return res.status(201).json({
        message: 'User registered successfully',
        token,
        user: {
          id: user._id,
          name: user.name,
          mobile: user.mobile,
          email: user.email,
          isVerified: user.isVerified,
          verificationStatus: user.verificationStatus
        }
      });
    }

    // MongoDB logic
    const existingUser = await User.findOne({
      $or: [{ email }, { mobile }]
    });

    if (existingUser) {
      return res.status(400).json({
        message: 'User already exists with this email or mobile number'
      });
    }

    const user = new User({
      name,
      mobile,
      email,
      password
    });

    await user.save();
    const token = generateToken(user._id);

    res.status(201).json({
      message: 'User registered successfully',
      token,
      user: {
        id: user._id,
        name: user.name,
        mobile: user.mobile,
        email: user.email,
        isVerified: user.isVerified,
        verificationStatus: user.verificationStatus
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: error.message || 'Server error during registration' });
  }
});

// Login user
router.post('/login', async (req, res) => {
  try {
    const { mobile, password } = req.body;

    // Use file storage if MongoDB is not available
    if (req.app.locals.useFileStorage) {
      const user = fileStorage.findUserByMobile(mobile);
      if (!user) {
        return res.status(400).json({ message: 'Invalid mobile number or password' });
      }

      const isMatch = await bcrypt.compare(password, user.password);
      if (!isMatch) {
        return res.status(400).json({ message: 'Invalid mobile number or password' });
      }

      const token = generateToken(user._id);
      return res.json({
        message: 'Login successful',
        token,
        user: {
          id: user._id,
          name: user.name,
          mobile: user.mobile,
          email: user.email,
          isVerified: user.isVerified,
          verificationStatus: user.verificationStatus,
          documentsUploaded: user.documentsUploaded
        }
      });
    }

    // MongoDB logic
    const user = await User.findOne({ mobile });
    if (!user) {
      return res.status(400).json({ message: 'Invalid mobile number or password' });
    }

    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return res.status(400).json({ message: 'Invalid mobile number or password' });
    }

    const token = generateToken(user._id);

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user._id,
        name: user.name,
        mobile: user.mobile,
        email: user.email,
        isVerified: user.isVerified,
        verificationStatus: user.verificationStatus,
        documentsUploaded: user.documentsUploaded
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Server error during login' });
  }
});

// Get current user
router.get('/me', auth, async (req, res) => {
  try {
    // Use file storage if MongoDB is not available
    if (req.app.locals.useFileStorage) {
      const user = fileStorage.findUserById(req.user._id);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      return res.json({
        user: {
          id: user._id,
          name: user.name,
          mobile: user.mobile,
          email: user.email,
          isVerified: user.isVerified,
          verificationStatus: user.verificationStatus,
          documentsUploaded: user.documentsUploaded
        }
      });
    }

    // MongoDB logic
    res.json({
      user: {
        id: req.user._id,
        name: req.user.name,
        mobile: req.user.mobile,
        email: req.user.email,
        isVerified: req.user.isVerified,
        verificationStatus: req.user.verificationStatus,
        documentsUploaded: req.user.documentsUploaded
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
