import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Card,
  CardContent,
  CardActions,
  Grid,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  AppBar,
  Toolbar,
  IconButton,
  Badge,
  Alert
} from '@mui/material';
import {
  AdminPanelSettings,
  Logout,
  Visibility,
  CheckCircle,
  Cancel,
  Pending,
  Refresh
} from '@mui/icons-material';

const AdminDashboard = ({ onLogout }) => {
  const [documents, setDocuments] = useState([]);
  const [selectedDoc, setSelectedDoc] = useState(null);
  const [viewDialog, setViewDialog] = useState(false);
  const [rejectDialog, setRejectDialog] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    fetchPendingDocuments();
  }, []);

  const fetchPendingDocuments = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/pending-documents', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const data = await response.json();
      if (response.ok) {
        setDocuments(data.documents || []);
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
    }
  };

  const handleApprove = async (docId) => {
    setLoading(true);
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/approve-document/${docId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const data = await response.json();
      if (response.ok) {
        setMessage('Document approved successfully!');
        fetchPendingDocuments();
      } else {
        setMessage(data.message || 'Error approving document');
      }
    } catch (error) {
      setMessage('Network error');
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      setMessage('Please provide a reason for rejection');
      return;
    }

    setLoading(true);
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/reject-document/${selectedDoc._id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reason: rejectReason })
      });
      const data = await response.json();
      if (response.ok) {
        setMessage('Document rejected successfully!');
        setRejectDialog(false);
        setRejectReason('');
        fetchPendingDocuments();
      } else {
        setMessage(data.message || 'Error rejecting document');
      }
    } catch (error) {
      setMessage('Network error');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved': return 'success';
      case 'rejected': return 'error';
      default: return 'warning';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'approved': return <CheckCircle />;
      case 'rejected': return <Cancel />;
      default: return <Pending />;
    }
  };

  const pendingCount = documents.filter(doc => doc.status === 'pending').length;

  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <AdminPanelSettings sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Admin Dashboard - Document Verification
          </Typography>
          <Badge badgeContent={pendingCount} color="error" sx={{ mr: 2 }}>
            <Pending />
          </Badge>
          <IconButton color="inherit" onClick={fetchPendingDocuments}>
            <Refresh />
          </IconButton>
          <IconButton color="inherit" onClick={onLogout}>
            <Logout />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {message && (
          <Alert 
            severity={message.includes('successfully') ? 'success' : 'error'} 
            sx={{ mb: 2 }}
            onClose={() => setMessage('')}
          >
            {message}
          </Alert>
        )}

        <Typography variant="h4" gutterBottom>
          Document Verification Queue
        </Typography>
        
        <Typography variant="body1" color="text.secondary" gutterBottom>
          {pendingCount} documents pending verification
        </Typography>

        <Grid container spacing={3} sx={{ mt: 2 }}>
          {documents.map((doc) => (
            <Grid item xs={12} md={6} lg={4} key={doc._id}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6">
                      {doc.userName || 'User'}
                    </Typography>
                    <Chip
                      icon={getStatusIcon(doc.status)}
                      label={doc.status.toUpperCase()}
                      color={getStatusColor(doc.status)}
                      size="small"
                    />
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Mobile: {doc.userMobile || 'N/A'}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Uploaded: {new Date(doc.createdAt).toLocaleDateString()}
                  </Typography>

                  <Typography variant="body2" gutterBottom>
                    Bank: {doc.bankDetails?.accountNumber || 'N/A'}
                  </Typography>
                </CardContent>
                
                <CardActions>
                  <Button
                    size="small"
                    startIcon={<Visibility />}
                    onClick={() => {
                      setSelectedDoc(doc);
                      setViewDialog(true);
                    }}
                  >
                    View
                  </Button>
                  
                  {doc.status === 'pending' && (
                    <>
                      <Button
                        size="small"
                        color="success"
                        startIcon={<CheckCircle />}
                        onClick={() => handleApprove(doc._id)}
                        disabled={loading}
                      >
                        Approve
                      </Button>
                      <Button
                        size="small"
                        color="error"
                        startIcon={<Cancel />}
                        onClick={() => {
                          setSelectedDoc(doc);
                          setRejectDialog(true);
                        }}
                        disabled={loading}
                      >
                        Reject
                      </Button>
                    </>
                  )}
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>

        {documents.length === 0 && (
          <Paper sx={{ p: 4, textAlign: 'center', mt: 4 }}>
            <Typography variant="h6" color="text.secondary">
              No documents to review
            </Typography>
          </Paper>
        )}
      </Container>

      {/* View Document Dialog */}
      <Dialog open={viewDialog} onClose={() => setViewDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Document Details</DialogTitle>
        <DialogContent>
          {selectedDoc && (
            <Box>
              <Typography variant="h6" gutterBottom>
                User Information
              </Typography>
              <Typography>Name: {selectedDoc.userName || 'N/A'}</Typography>
              <Typography>Mobile: {selectedDoc.userMobile || 'N/A'}</Typography>
              <Typography>Email: {selectedDoc.userEmail || 'N/A'}</Typography>
              
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Bank Details
              </Typography>
              <Typography>Account: {selectedDoc.bankDetails?.accountNumber}</Typography>
              <Typography>IFSC: {selectedDoc.bankDetails?.ifscCode}</Typography>
              <Typography>Branch: {selectedDoc.bankDetails?.branchName}</Typography>
              
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Documents
              </Typography>
              <Typography>• Aadhar Card: {selectedDoc.aadharCard?.filename}</Typography>
              <Typography>• PAN Card: {selectedDoc.panCard?.filename}</Typography>
              <Typography>• Photo: {selectedDoc.photo?.filename}</Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={rejectDialog} onClose={() => setRejectDialog(false)}>
        <DialogTitle>Reject Document</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Reason for rejection"
            value={rejectReason}
            onChange={(e) => setRejectReason(e.target.value)}
            margin="normal"
            placeholder="Please provide a clear reason for rejection..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRejectDialog(false)}>Cancel</Button>
          <Button onClick={handleReject} color="error" disabled={loading}>
            Reject
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AdminDashboard;
