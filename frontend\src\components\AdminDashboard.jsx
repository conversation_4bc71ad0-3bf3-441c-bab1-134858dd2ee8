import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Card,
  CardContent,
  CardActions,
  Grid,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  AppBar,
  Toolbar,
  IconButton,
  Badge,
  Alert
} from '@mui/material';
import {
  AdminPanelSettings,
  Logout,
  Visibility,
  CheckCircle,
  Cancel,
  Pending,
  Refresh
} from '@mui/icons-material';

const AdminDashboard = ({ onLogout }) => {
  const [documents, setDocuments] = useState([]);
  const [selectedDoc, setSelectedDoc] = useState(null);
  const [viewDialog, setViewDialog] = useState(false);
  const [rejectDialog, setRejectDialog] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    fetchPendingDocuments();
  }, []);

  const fetchPendingDocuments = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch('/api/admin/pending-documents', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const data = await response.json();
      if (response.ok) {
        setDocuments(data.documents || []);
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
    }
  };

  const handleApprove = async (docId) => {
    setLoading(true);
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/approve-document/${docId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const data = await response.json();
      if (response.ok) {
        setMessage('Document approved successfully!');
        fetchPendingDocuments();
      } else {
        setMessage(data.message || 'Error approving document');
      }
    } catch (error) {
      setMessage('Network error');
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      setMessage('Please provide a reason for rejection');
      return;
    }

    setLoading(true);
    try {
      const token = localStorage.getItem('adminToken');
      const response = await fetch(`/api/admin/reject-document/${selectedDoc._id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reason: rejectReason })
      });
      const data = await response.json();
      if (response.ok) {
        setMessage('Document rejected successfully!');
        setRejectDialog(false);
        setRejectReason('');
        fetchPendingDocuments();
      } else {
        setMessage(data.message || 'Error rejecting document');
      }
    } catch (error) {
      setMessage('Network error');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved': return 'success';
      case 'rejected': return 'error';
      default: return 'warning';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'approved': return <CheckCircle />;
      case 'rejected': return <Cancel />;
      default: return <Pending />;
    }
  };

  const pendingCount = documents.filter(doc => doc.status === 'pending').length;

  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <AdminPanelSettings sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Admin Dashboard - Document Verification
          </Typography>
          <Badge badgeContent={pendingCount} color="error" sx={{ mr: 2 }}>
            <Pending />
          </Badge>
          <IconButton color="inherit" onClick={fetchPendingDocuments}>
            <Refresh />
          </IconButton>
          <IconButton color="inherit" onClick={onLogout}>
            <Logout />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {message && (
          <Alert
            severity={message.includes('successfully') ? 'success' : 'error'}
            sx={{ mb: 2 }}
            onClose={() => setMessage('')}
          >
            {message}
          </Alert>
        )}

        <Typography variant="h4" gutterBottom>
          Document Verification Queue
        </Typography>

        <Typography variant="body1" color="text.secondary" gutterBottom>
          {pendingCount} documents pending verification
        </Typography>

        <Grid container spacing={3} sx={{ mt: 2 }}>
          {documents.map((doc) => (
            <Grid item xs={12} md={6} lg={4} key={doc._id}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6">
                      {doc.userName || 'User'}
                    </Typography>
                    <Chip
                      icon={getStatusIcon(doc.status)}
                      label={doc.status.toUpperCase()}
                      color={getStatusColor(doc.status)}
                      size="small"
                    />
                  </Box>

                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Mobile: {doc.userMobile || 'N/A'}
                  </Typography>

                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Uploaded: {new Date(doc.createdAt).toLocaleDateString()}
                  </Typography>

                  <Typography variant="body2" gutterBottom>
                    Bank: {doc.bankDetails?.accountNumber || 'N/A'}
                  </Typography>
                </CardContent>

                <CardActions>
                  <Button
                    size="small"
                    startIcon={<Visibility />}
                    onClick={() => {
                      setSelectedDoc(doc);
                      setViewDialog(true);
                    }}
                  >
                    View
                  </Button>

                  {doc.status === 'pending' && (
                    <>
                      <Button
                        size="small"
                        color="success"
                        startIcon={<CheckCircle />}
                        onClick={() => handleApprove(doc._id)}
                        disabled={loading}
                      >
                        Approve
                      </Button>
                      <Button
                        size="small"
                        color="error"
                        startIcon={<Cancel />}
                        onClick={() => {
                          setSelectedDoc(doc);
                          setRejectDialog(true);
                        }}
                        disabled={loading}
                      >
                        Reject
                      </Button>
                    </>
                  )}
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>

        {documents.length === 0 && (
          <Paper sx={{ p: 4, textAlign: 'center', mt: 4 }}>
            <Typography variant="h6" color="text.secondary">
              No documents to review
            </Typography>
          </Paper>
        )}
      </Container>

      {/* View Document Dialog */}
      <Dialog open={viewDialog} onClose={() => setViewDialog(false)} maxWidth="lg" fullWidth>
        <DialogTitle>Document Details - {selectedDoc?.userName}</DialogTitle>
        <DialogContent>
          {selectedDoc && (
            <Box>
              <Grid container spacing={3}>
                {/* User Information */}
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom color="primary">
                      User Information
                    </Typography>
                    <Typography><strong>Name:</strong> {selectedDoc.userName || 'N/A'}</Typography>
                    <Typography><strong>Mobile:</strong> {selectedDoc.userMobile || 'N/A'}</Typography>
                    <Typography><strong>Email:</strong> {selectedDoc.userEmail || 'N/A'}</Typography>
                    <Typography><strong>Uploaded:</strong> {new Date(selectedDoc.createdAt).toLocaleString()}</Typography>
                  </Paper>
                </Grid>

                {/* Bank Details */}
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom color="primary">
                      Bank Details
                    </Typography>
                    <Typography><strong>Account:</strong> {selectedDoc.bankDetails?.accountNumber}</Typography>
                    <Typography><strong>IFSC:</strong> {selectedDoc.bankDetails?.ifscCode}</Typography>
                    <Typography><strong>Branch:</strong> {selectedDoc.bankDetails?.branchName}</Typography>
                  </Paper>
                </Grid>

                {/* Document Images */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom color="primary">
                    Uploaded Documents
                  </Typography>

                  <Grid container spacing={2}>
                    {/* Aadhar Card */}
                    <Grid item xs={12} md={4}>
                      <Paper sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="subtitle1" gutterBottom>
                          <strong>Aadhar Card</strong>
                        </Typography>
                        {selectedDoc.aadharCard?.filename && (
                          <Box>
                            <img
                              src={`http://localhost:5000/uploads/documents/${selectedDoc.aadharCard.filename}`}
                              alt="Aadhar Card"
                              style={{
                                maxWidth: '100%',
                                maxHeight: '200px',
                                objectFit: 'contain',
                                border: '1px solid #ddd',
                                borderRadius: '4px'
                              }}
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'block';
                              }}
                            />
                            <Typography
                              variant="body2"
                              color="error"
                              sx={{ display: 'none', mt: 1 }}
                            >
                              Image not available
                            </Typography>
                            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                              {selectedDoc.aadharCard.filename}
                            </Typography>
                            <Button
                              size="small"
                              variant="outlined"
                              sx={{ mt: 1 }}
                              onClick={() => window.open(`http://localhost:5000/uploads/documents/${selectedDoc.aadharCard.filename}`, '_blank')}
                            >
                              View Full Size
                            </Button>
                          </Box>
                        )}
                      </Paper>
                    </Grid>

                    {/* PAN Card */}
                    <Grid item xs={12} md={4}>
                      <Paper sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="subtitle1" gutterBottom>
                          <strong>PAN Card</strong>
                        </Typography>
                        {selectedDoc.panCard?.filename && (
                          <Box>
                            <img
                              src={`http://localhost:5000/uploads/documents/${selectedDoc.panCard.filename}`}
                              alt="PAN Card"
                              style={{
                                maxWidth: '100%',
                                maxHeight: '200px',
                                objectFit: 'contain',
                                border: '1px solid #ddd',
                                borderRadius: '4px'
                              }}
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'block';
                              }}
                            />
                            <Typography
                              variant="body2"
                              color="error"
                              sx={{ display: 'none', mt: 1 }}
                            >
                              Image not available
                            </Typography>
                            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                              {selectedDoc.panCard.filename}
                            </Typography>
                            <Button
                              size="small"
                              variant="outlined"
                              sx={{ mt: 1 }}
                              onClick={() => window.open(`http://localhost:5000/uploads/documents/${selectedDoc.panCard.filename}`, '_blank')}
                            >
                              View Full Size
                            </Button>
                          </Box>
                        )}
                      </Paper>
                    </Grid>

                    {/* Photo */}
                    <Grid item xs={12} md={4}>
                      <Paper sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="subtitle1" gutterBottom>
                          <strong>User Photo</strong>
                        </Typography>
                        {selectedDoc.photo?.filename && (
                          <Box>
                            <img
                              src={`http://localhost:5000/uploads/documents/${selectedDoc.photo.filename}`}
                              alt="User Photo"
                              style={{
                                maxWidth: '100%',
                                maxHeight: '200px',
                                objectFit: 'contain',
                                border: '1px solid #ddd',
                                borderRadius: '4px'
                              }}
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'block';
                              }}
                            />
                            <Typography
                              variant="body2"
                              color="error"
                              sx={{ display: 'none', mt: 1 }}
                            >
                              Image not available
                            </Typography>
                            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                              {selectedDoc.photo.filename}
                            </Typography>
                            <Button
                              size="small"
                              variant="outlined"
                              sx={{ mt: 1 }}
                              onClick={() => window.open(`http://localhost:5000/uploads/documents/${selectedDoc.photo.filename}`, '_blank')}
                            >
                              View Full Size
                            </Button>
                          </Box>
                        )}
                      </Paper>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialog(false)}>Close</Button>
          {selectedDoc?.status === 'pending' && (
            <>
              <Button
                color="success"
                variant="contained"
                startIcon={<CheckCircle />}
                onClick={() => {
                  setViewDialog(false);
                  handleApprove(selectedDoc._id);
                }}
                disabled={loading}
              >
                Approve
              </Button>
              <Button
                color="error"
                variant="contained"
                startIcon={<Cancel />}
                onClick={() => {
                  setViewDialog(false);
                  setRejectDialog(true);
                }}
                disabled={loading}
              >
                Reject
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={rejectDialog} onClose={() => setRejectDialog(false)}>
        <DialogTitle>Reject Document</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Reason for rejection"
            value={rejectReason}
            onChange={(e) => setRejectReason(e.target.value)}
            margin="normal"
            placeholder="Please provide a clear reason for rejection..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRejectDialog(false)}>Cancel</Button>
          <Button onClick={handleReject} color="error" disabled={loading}>
            Reject
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AdminDashboard;
