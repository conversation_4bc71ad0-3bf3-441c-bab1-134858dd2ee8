const express = require('express');
const jwt = require('jsonwebtoken');
const Document = require('../models/Document');
const User = require('../models/User');
const fileStorage = require('../utils/fileStorage');

const router = express.Router();

// Admin credentials (in real app, store in database with hashed password)
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123' // In real app, hash this password
};

// Admin login
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password) {
      const token = jwt.sign(
        { isAdmin: true, username },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: '24h' }
      );

      res.json({
        message: 'Login successful',
        token
      });
    } else {
      res.status(401).json({ message: 'Invalid credentials' });
    }
  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Verify admin token
router.get('/verify', (req, res) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({ message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    if (decoded.isAdmin) {
      res.json({ message: 'Token valid', admin: decoded.username });
    } else {
      res.status(401).json({ message: 'Not an admin' });
    }
  } catch (error) {
    res.status(401).json({ message: 'Invalid token' });
  }
});

// Middleware to verify admin
const verifyAdmin = (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({ message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    if (decoded.isAdmin) {
      req.admin = decoded;
      next();
    } else {
      res.status(401).json({ message: 'Not an admin' });
    }
  } catch (error) {
    res.status(401).json({ message: 'Invalid token' });
  }
};

// Get all pending documents
router.get('/pending-documents', verifyAdmin, async (req, res) => {
  try {
    // Use file storage if MongoDB is not available
    if (req.app.locals.useFileStorage) {
      const documents = fileStorage.getAllDocuments();
      const users = fileStorage.getAllUsers();
      
      // Enrich documents with user data
      const enrichedDocs = documents.map(doc => {
        const user = users.find(u => u._id === doc.userId);
        return {
          ...doc,
          userName: user?.name,
          userMobile: user?.mobile,
          userEmail: user?.email
        };
      });

      return res.json({ documents: enrichedDocs });
    }

    // MongoDB logic
    const documents = await Document.find()
      .populate('userId', 'name mobile email')
      .sort({ createdAt: -1 });

    const enrichedDocs = documents.map(doc => ({
      _id: doc._id,
      userId: doc.userId._id,
      userName: doc.userId.name,
      userMobile: doc.userId.mobile,
      userEmail: doc.userId.email,
      aadharCard: doc.aadharCard,
      panCard: doc.panCard,
      photo: doc.photo,
      bankDetails: doc.bankDetails,
      status: doc.status,
      createdAt: doc.createdAt,
      approvedAt: doc.approvedAt,
      rejectionReason: doc.rejectionReason
    }));

    res.json({ documents: enrichedDocs });
  } catch (error) {
    console.error('Get pending documents error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Approve document
router.put('/approve-document/:documentId', verifyAdmin, async (req, res) => {
  try {
    // Use file storage if MongoDB is not available
    if (req.app.locals.useFileStorage) {
      const document = fileStorage.updateDocument(req.params.documentId, {
        status: 'approved',
        approvedAt: new Date().toISOString()
      });

      if (!document) {
        return res.status(404).json({ message: 'Document not found' });
      }

      // Update user verification status
      fileStorage.updateUser(document.userId, {
        isVerified: true,
        verificationStatus: 'approved'
      });

      return res.json({ message: 'Document approved successfully' });
    }

    // MongoDB logic
    const document = await Document.findById(req.params.documentId);
    
    if (!document) {
      return res.status(404).json({ message: 'Document not found' });
    }

    document.status = 'approved';
    document.approvedAt = new Date();
    await document.save();

    await User.findByIdAndUpdate(document.userId, {
      isVerified: true,
      verificationStatus: 'approved'
    });

    res.json({ message: 'Document approved successfully' });
  } catch (error) {
    console.error('Document approval error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Reject document
router.put('/reject-document/:documentId', verifyAdmin, async (req, res) => {
  try {
    const { reason } = req.body;

    if (!reason || !reason.trim()) {
      return res.status(400).json({ message: 'Rejection reason is required' });
    }

    // Use file storage if MongoDB is not available
    if (req.app.locals.useFileStorage) {
      const document = fileStorage.updateDocument(req.params.documentId, {
        status: 'rejected',
        rejectionReason: reason,
        rejectedAt: new Date().toISOString()
      });

      if (!document) {
        return res.status(404).json({ message: 'Document not found' });
      }

      // Update user verification status
      fileStorage.updateUser(document.userId, {
        isVerified: false,
        verificationStatus: 'rejected'
      });

      return res.json({ message: 'Document rejected successfully' });
    }

    // MongoDB logic
    const document = await Document.findById(req.params.documentId);
    
    if (!document) {
      return res.status(404).json({ message: 'Document not found' });
    }

    document.status = 'rejected';
    document.rejectionReason = reason;
    document.rejectedAt = new Date();
    await document.save();

    await User.findByIdAndUpdate(document.userId, {
      isVerified: false,
      verificationStatus: 'rejected'
    });

    res.json({ message: 'Document rejected successfully' });
  } catch (error) {
    console.error('Document rejection error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
