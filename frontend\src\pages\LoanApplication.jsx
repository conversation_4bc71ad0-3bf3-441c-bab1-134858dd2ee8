import React, { useState } from 'react'
import {
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Box,
  Alert,
  InputAdornment,
  Slider,
  Grid
} from '@mui/material'
import { AttachMoney } from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import Layout from '../components/Layout'
import axios from 'axios'

const LoanApplication = () => {
  const navigate = useNavigate()
  const [loanAmount, setLoanAmount] = useState(10000)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [loading, setLoading] = useState(false)

  const handleSliderChange = (event, newValue) => {
    setLoanAmount(newValue)
  }

  const handleInputChange = (event) => {
    const value = event.target.value === '' ? 0 : Number(event.target.value)
    if (value >= 1000 && value <= 500000) {
      setLoanAmount(value)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')
    setSuccess('')
    setLoading(true)

    if (loanAmount < 1000) {
      setError('Minimum loan amount is ₹1,000')
      setLoading(false)
      return
    }

    if (loanAmount > 500000) {
      setError('Maximum loan amount is ₹5,00,000')
      setLoading(false)
      return
    }

    try {
      const response = await axios.post('/api/loans/apply', {
        loanAmount: loanAmount
      })

      setSuccess('Loan application submitted successfully!')
      setTimeout(() => {
        navigate('/dashboard')
      }, 2000)
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to submit loan application')
    } finally {
      setLoading(false)
    }
  }

  const processingFee = 1500
  const interestRate = 12 // Annual interest rate
  const tenure = 12 // months
  const monthlyEMI = Math.round((loanAmount * interestRate * Math.pow(1 + interestRate/100/12, tenure)) / (100 * (Math.pow(1 + interestRate/100/12, tenure) - 1)))

  return (
    <Layout title="Loan Application">
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom>
                Apply for Loan
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Choose your desired loan amount. A processing fee of ₹{processingFee} will be required.
              </Typography>

              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              {success && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  {success}
                </Alert>
              )}

              <Box component="form" onSubmit={handleSubmit}>
                <Box sx={{ mb: 4 }}>
                  <Typography variant="h6" gutterBottom>
                    Loan Amount
                  </Typography>
                  
                  <TextField
                    fullWidth
                    label="Loan Amount"
                    value={loanAmount}
                    onChange={handleInputChange}
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                    }}
                    sx={{ mb: 3 }}
                    type="number"
                    inputProps={{
                      min: 1000,
                      max: 500000,
                      step: 1000
                    }}
                  />

                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Use slider to adjust amount (₹1,000 - ₹5,00,000)
                  </Typography>
                  
                  <Slider
                    value={loanAmount}
                    onChange={handleSliderChange}
                    min={1000}
                    max={500000}
                    step={1000}
                    marks={[
                      { value: 1000, label: '₹1K' },
                      { value: 100000, label: '₹1L' },
                      { value: 250000, label: '₹2.5L' },
                      { value: 500000, label: '₹5L' }
                    ]}
                    valueLabelDisplay="auto"
                    valueLabelFormat={(value) => `₹${value.toLocaleString()}`}
                  />
                </Box>

                <Button
                  type="submit"
                  variant="contained"
                  size="large"
                  disabled={loading}
                  fullWidth
                  startIcon={<AttachMoney />}
                >
                  {loading ? 'Submitting...' : `Apply for ₹${loanAmount.toLocaleString()}`}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Loan Summary
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Loan Amount
                </Typography>
                <Typography variant="h6">
                  ₹{loanAmount.toLocaleString()}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Processing Fee
                </Typography>
                <Typography variant="h6" color="error">
                  ₹{processingFee.toLocaleString()}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Interest Rate (Annual)
                </Typography>
                <Typography variant="h6">
                  {interestRate}%
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Tenure
                </Typography>
                <Typography variant="h6">
                  {tenure} months
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Estimated Monthly EMI
                </Typography>
                <Typography variant="h6" color="primary">
                  ₹{monthlyEMI.toLocaleString()}
                </Typography>
              </Box>

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  You will need to pay the processing fee of ₹{processingFee} before loan approval.
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Layout>
  )
}

export default LoanApplication
