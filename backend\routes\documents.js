const express = require('express');
const Document = require('../models/Document');
const User = require('../models/User');
const auth = require('../middleware/auth');
const upload = require('../config/multer');
const fileStorage = require('../utils/fileStorage');

const router = express.Router();

// Upload documents
router.post('/upload', auth, upload.fields([
  { name: 'aadharCard', maxCount: 1 },
  { name: 'panCard', maxCount: 1 },
  { name: 'photo', maxCount: 1 }
]), async (req, res) => {
  try {
    const { accountNumber, ifscCode, branchName } = req.body;
    const files = req.files;

    // Check if all required files are uploaded
    if (!files.aadharCard || !files.panCard || !files.photo) {
      return res.status(400).json({
        message: 'All documents (Aadhar Card, PAN Card, and Photo) are required'
      });
    }

    // Check if bank details are provided
    if (!accountNumber || !ifscCode || !branchName) {
      return res.status(400).json({
        message: 'Bank details (Account Number, IFSC Code, Branch Name) are required'
      });
    }

    // Use file storage if MongoDB is not available
    if (req.app.locals.useFileStorage) {
      // Check if user already has documents
      const existingDoc = fileStorage.findDocumentByUserId(req.user._id);
      if (existingDoc) {
        return res.status(400).json({
          message: 'Documents already uploaded for this user'
        });
      }

      // Create document record
      const documentData = {
        userId: req.user._id,
        aadharCard: {
          filename: files.aadharCard[0].filename,
          path: files.aadharCard[0].path,
          uploadedAt: new Date()
        },
        panCard: {
          filename: files.panCard[0].filename,
          path: files.panCard[0].path,
          uploadedAt: new Date()
        },
        photo: {
          filename: files.photo[0].filename,
          path: files.photo[0].path,
          uploadedAt: new Date()
        },
        bankDetails: {
          accountNumber,
          ifscCode,
          branchName
        },
        status: 'pending'
      };

      const document = fileStorage.createDocument(documentData);

      // Update user's documentsUploaded status
      fileStorage.updateUser(req.user._id, { documentsUploaded: true });

      return res.status(201).json({
        message: 'Documents uploaded successfully',
        document: {
          id: document._id,
          status: document.status,
          uploadedAt: document.createdAt
        }
      });
    }

    // MongoDB logic
    const existingDoc = await Document.findOne({ userId: req.user._id });
    if (existingDoc) {
      return res.status(400).json({
        message: 'Documents already uploaded for this user'
      });
    }

    const document = new Document({
      userId: req.user._id,
      aadharCard: {
        filename: files.aadharCard[0].filename,
        path: files.aadharCard[0].path,
        uploadedAt: new Date()
      },
      panCard: {
        filename: files.panCard[0].filename,
        path: files.panCard[0].path,
        uploadedAt: new Date()
      },
      photo: {
        filename: files.photo[0].filename,
        path: files.photo[0].path,
        uploadedAt: new Date()
      },
      bankDetails: {
        accountNumber,
        ifscCode,
        branchName
      }
    });

    await document.save();
    await User.findByIdAndUpdate(req.user._id, { documentsUploaded: true });

    res.status(201).json({
      message: 'Documents uploaded successfully',
      document: {
        id: document._id,
        status: document.status,
        uploadedAt: document.createdAt
      }
    });
  } catch (error) {
    console.error('Document upload error:', error);
    res.status(500).json({ message: 'Server error during document upload' });
  }
});

// Get user documents
router.get('/my-documents', auth, async (req, res) => {
  try {
    // Use file storage if MongoDB is not available
    if (req.app.locals.useFileStorage) {
      const document = fileStorage.findDocumentByUserId(req.user._id);

      if (!document) {
        return res.status(404).json({ message: 'No documents found' });
      }

      return res.json({
        document: {
          id: document._id,
          status: document.status,
          bankDetails: document.bankDetails,
          uploadedAt: document.createdAt,
          approvedAt: document.approvedAt,
          rejectionReason: document.rejectionReason
        }
      });
    }

    // MongoDB logic
    const document = await Document.findOne({ userId: req.user._id });

    if (!document) {
      return res.status(404).json({ message: 'No documents found' });
    }

    res.json({
      document: {
        id: document._id,
        status: document.status,
        bankDetails: document.bankDetails,
        uploadedAt: document.createdAt,
        approvedAt: document.approvedAt,
        rejectionReason: document.rejectionReason
      }
    });
  } catch (error) {
    console.error('Get documents error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Approve documents (Admin only - for testing, we'll make it simple)
router.put('/approve/:documentId', async (req, res) => {
  try {
    const document = await Document.findById(req.params.documentId);

    if (!document) {
      return res.status(404).json({ message: 'Document not found' });
    }

    document.status = 'approved';
    document.approvedAt = new Date();
    await document.save();

    // Update user verification status
    await User.findByIdAndUpdate(document.userId, {
      isVerified: true,
      verificationStatus: 'approved'
    });

    res.json({ message: 'Documents approved successfully' });
  } catch (error) {
    console.error('Document approval error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
