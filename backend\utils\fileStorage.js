const fs = require('fs');
const path = require('path');
const bcrypt = require('bcryptjs');

const dataDir = path.join(__dirname, '../data');

// Ensure data directory exists
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// File paths
const usersFile = path.join(dataDir, 'users.json');
const documentsFile = path.join(dataDir, 'documents.json');
const loansFile = path.join(dataDir, 'loans.json');
const paymentsFile = path.join(dataDir, 'payments.json');

// Helper functions
const readFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      return JSON.parse(fs.readFileSync(filePath, 'utf8'));
    }
    return [];
  } catch (error) {
    console.error('Error reading file:', error);
    return [];
  }
};

const writeFile = (filePath, data) => {
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing file:', error);
    return false;
  }
};

const generateId = () => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

// User operations
const createUser = async (userData) => {
  const users = readFile(usersFile);
  
  // Check if user exists
  const existingUser = users.find(u => u.email === userData.email || u.mobile === userData.mobile);
  if (existingUser) {
    throw new Error('User already exists with this email or mobile number');
  }

  // Hash password
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash(userData.password, salt);

  const newUser = {
    _id: generateId(),
    name: userData.name,
    mobile: userData.mobile,
    email: userData.email,
    password: hashedPassword,
    isVerified: false,
    verificationStatus: 'pending',
    documentsUploaded: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  users.push(newUser);
  writeFile(usersFile, users);
  
  // Return user without password
  const { password, ...userWithoutPassword } = newUser;
  return userWithoutPassword;
};

const findUserByMobile = (mobile) => {
  const users = readFile(usersFile);
  return users.find(u => u.mobile === mobile);
};

const findUserById = (id) => {
  const users = readFile(usersFile);
  const user = users.find(u => u._id === id);
  if (user) {
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }
  return null;
};

const updateUser = (id, updateData) => {
  const users = readFile(usersFile);
  const userIndex = users.findIndex(u => u._id === id);
  
  if (userIndex !== -1) {
    users[userIndex] = { ...users[userIndex], ...updateData, updatedAt: new Date().toISOString() };
    writeFile(usersFile, users);
    const { password, ...userWithoutPassword } = users[userIndex];
    return userWithoutPassword;
  }
  return null;
};

// Document operations
const createDocument = (docData) => {
  const documents = readFile(documentsFile);
  const newDoc = {
    _id: generateId(),
    ...docData,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  documents.push(newDoc);
  writeFile(documentsFile, documents);
  return newDoc;
};

const findDocumentByUserId = (userId) => {
  const documents = readFile(documentsFile);
  return documents.find(d => d.userId === userId);
};

// Loan operations
const createLoan = (loanData) => {
  const loans = readFile(loansFile);
  const newLoan = {
    _id: generateId(),
    ...loanData,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  loans.push(newLoan);
  writeFile(loansFile, loans);
  return newLoan;
};

const findLoansByUserId = (userId) => {
  const loans = readFile(loansFile);
  return loans.filter(l => l.userId === userId);
};

const findLoanById = (id) => {
  const loans = readFile(loansFile);
  return loans.find(l => l._id === id);
};

const updateLoan = (id, updateData) => {
  const loans = readFile(loansFile);
  const loanIndex = loans.findIndex(l => l._id === id);
  
  if (loanIndex !== -1) {
    loans[loanIndex] = { ...loans[loanIndex], ...updateData, updatedAt: new Date().toISOString() };
    writeFile(loansFile, loans);
    return loans[loanIndex];
  }
  return null;
};

// Payment operations
const createPayment = (paymentData) => {
  const payments = readFile(paymentsFile);
  const newPayment = {
    _id: generateId(),
    ...paymentData,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  payments.push(newPayment);
  writeFile(paymentsFile, payments);
  return newPayment;
};

const findPaymentByLoanId = (loanId) => {
  const payments = readFile(paymentsFile);
  return payments.find(p => p.loanId === loanId);
};

module.exports = {
  createUser,
  findUserByMobile,
  findUserById,
  updateUser,
  createDocument,
  findDocumentByUserId,
  createLoan,
  findLoansByUserId,
  findLoanById,
  updateLoan,
  createPayment,
  findPaymentByLoanId
};
