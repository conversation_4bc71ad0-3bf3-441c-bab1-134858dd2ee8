import React, { useState } from 'react'
import {
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  Alert,
  LinearProgress
} from '@mui/material'
import { CloudUpload } from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import Layout from '../components/Layout'
import axios from 'axios'

const DocumentUpload = () => {
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    accountNumber: '',
    ifscCode: '',
    branchName: ''
  })
  const [files, setFiles] = useState({
    aadharCard: null,
    panCard: null,
    photo: null
  })
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [loading, setLoading] = useState(false)

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleFileChange = (e) => {
    const { name, files: selectedFiles } = e.target
    if (selectedFiles && selectedFiles[0]) {
      setFiles({
        ...files,
        [name]: selectedFiles[0]
      })
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')
    setSuccess('')
    setLoading(true)

    // Validation
    if (!formData.accountNumber || !formData.ifscCode || !formData.branchName) {
      setError('All bank details are required')
      setLoading(false)
      return
    }

    if (!files.aadharCard || !files.panCard || !files.photo) {
      setError('All documents (Aadhar Card, PAN Card, and Photo) are required')
      setLoading(false)
      return
    }

    try {
      const uploadData = new FormData()
      uploadData.append('accountNumber', formData.accountNumber)
      uploadData.append('ifscCode', formData.ifscCode)
      uploadData.append('branchName', formData.branchName)
      uploadData.append('aadharCard', files.aadharCard)
      uploadData.append('panCard', files.panCard)
      uploadData.append('photo', files.photo)

      const response = await axios.post('/api/documents/upload', uploadData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      setSuccess('Documents uploaded successfully! Your documents are now under review.')
      setTimeout(() => {
        navigate('/dashboard')
      }, 2000)
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to upload documents')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Layout title="Upload Documents">
      <Card>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Upload Your Documents
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Please upload all required documents and provide your bank details for verification.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          {loading && <LinearProgress sx={{ mb: 2 }} />}

          <Box component="form" onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              {/* Bank Details Section */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Bank Details
                </Typography>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Bank Account Number"
                  name="accountNumber"
                  value={formData.accountNumber}
                  onChange={handleInputChange}
                  required
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="IFSC Code"
                  name="ifscCode"
                  value={formData.ifscCode}
                  onChange={handleInputChange}
                  required
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Branch Name"
                  name="branchName"
                  value={formData.branchName}
                  onChange={handleInputChange}
                  required
                />
              </Grid>

              {/* Document Upload Section */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                  Document Upload
                </Typography>
              </Grid>

              {/* Aadhar Card */}
              <Grid item xs={12} md={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Aadhar Card *
                    </Typography>
                    <Button
                      component="label"
                      variant="outlined"
                      startIcon={<CloudUpload />}
                      fullWidth
                      sx={{ mb: 1 }}
                    >
                      Choose File
                      <input
                        type="file"
                        name="aadharCard"
                        accept="image/*"
                        onChange={handleFileChange}
                        hidden
                      />
                    </Button>
                    {files.aadharCard && (
                      <Typography variant="body2" color="text.secondary">
                        {files.aadharCard.name}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {/* PAN Card */}
              <Grid item xs={12} md={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      PAN Card *
                    </Typography>
                    <Button
                      component="label"
                      variant="outlined"
                      startIcon={<CloudUpload />}
                      fullWidth
                      sx={{ mb: 1 }}
                    >
                      Choose File
                      <input
                        type="file"
                        name="panCard"
                        accept="image/*"
                        onChange={handleFileChange}
                        hidden
                      />
                    </Button>
                    {files.panCard && (
                      <Typography variant="body2" color="text.secondary">
                        {files.panCard.name}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {/* Photo */}
              <Grid item xs={12} md={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Your Photo *
                    </Typography>
                    <Button
                      component="label"
                      variant="outlined"
                      startIcon={<CloudUpload />}
                      fullWidth
                      sx={{ mb: 1 }}
                    >
                      Choose File
                      <input
                        type="file"
                        name="photo"
                        accept="image/*"
                        onChange={handleFileChange}
                        hidden
                      />
                    </Button>
                    {files.photo && (
                      <Typography variant="body2" color="text.secondary">
                        {files.photo.name}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>

              {/* Submit Button */}
              <Grid item xs={12}>
                <Box sx={{ mt: 3 }}>
                  <Button
                    type="submit"
                    variant="contained"
                    size="large"
                    disabled={loading}
                    fullWidth
                  >
                    {loading ? 'Uploading...' : 'Submit Documents'}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>
    </Layout>
  )
}

export default DocumentUpload
