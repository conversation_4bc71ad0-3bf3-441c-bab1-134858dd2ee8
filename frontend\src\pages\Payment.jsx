import React, { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Alert,
  Grid,
  TextField,
  Stepper,
  Step,
  StepLabel,
  Paper
} from '@mui/material'
import { QrCode, CloudUpload, Payment as PaymentIcon } from '@mui/icons-material'
import { useParams, useNavigate } from 'react-router-dom'
import Layout from '../components/Layout'
import axios from 'axios'

const Payment = () => {
  const { loanId } = useParams()
  const navigate = useNavigate()
  const [loan, setLoan] = useState(null)
  const [qrCode, setQrCode] = useState(null)
  const [activeStep, setActiveStep] = useState(0)
  const [utrNumber, setUtrNumber] = useState('')
  const [screenshot, setScreenshot] = useState(null)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [loading, setLoading] = useState(false)

  const steps = ['View QR Code', 'Make Payment', 'Upload Proof']

  useEffect(() => {
    fetchLoanDetails()
    fetchQRCode()
  }, [loanId])

  const fetchLoanDetails = async () => {
    try {
      const response = await axios.get(`/api/loans/${loanId}`)
      setLoan(response.data.loan)
    } catch (error) {
      setError('Failed to fetch loan details')
    }
  }

  const fetchQRCode = async () => {
    try {
      const response = await axios.get(`/api/payments/qr-code/${loanId}`)
      setQrCode(response.data.qrCode)
    } catch (error) {
      setError('Failed to generate QR code')
    }
  }

  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setScreenshot(e.target.files[0])
    }
  }

  const handleSubmitPayment = async (e) => {
    e.preventDefault()
    setError('')
    setSuccess('')
    setLoading(true)

    if (!utrNumber || !screenshot) {
      setError('UTR number and payment screenshot are required')
      setLoading(false)
      return
    }

    try {
      const formData = new FormData()
      formData.append('loanId', loanId)
      formData.append('utrNumber', utrNumber)
      formData.append('screenshot', screenshot)

      const response = await axios.post('/api/payments/submit', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      setSuccess('Payment proof submitted successfully!')
      setTimeout(() => {
        navigate(`/payment-status/${loanId}`)
      }, 2000)
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to submit payment proof')
    } finally {
      setLoading(false)
    }
  }

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1)
  }

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1)
  }

  if (!loan || !qrCode) {
    return (
      <Layout title="Payment">
        <Typography>Loading...</Typography>
      </Layout>
    )
  }

  return (
    <Layout title="Payment">
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom>
                Payment for Loan Application
              </Typography>
              <Typography variant="body1" paragraph>
                Loan Amount: ₹{loan.loanAmount.toLocaleString()}
              </Typography>
              <Typography variant="h6" color="error" paragraph>
                Processing Fee: ₹{loan.processingFee}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Grid>

        <Grid item xs={12}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          {activeStep === 0 && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Step 1: QR Code for Payment
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Scan the QR code below with any UPI app to make the payment of ₹{loan.processingFee}
                </Typography>
                
                <Box display="flex" justifyContent="center" sx={{ mb: 3 }}>
                  <Paper elevation={3} sx={{ p: 3, textAlign: 'center' }}>
                    <QrCode sx={{ fontSize: 200, color: 'primary.main' }} />
                    <Typography variant="body2" sx={{ mt: 2 }}>
                      QR Code for ₹{loan.processingFee}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {qrCode.merchantName}
                    </Typography>
                  </Paper>
                </Box>

                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Payment Details:</strong><br />
                    Amount: ₹{qrCode.amount}<br />
                    Merchant: {qrCode.merchantName}<br />
                    UPI ID: {qrCode.merchantUPI}
                  </Typography>
                </Alert>

                <Button
                  variant="contained"
                  onClick={handleNext}
                  fullWidth
                  startIcon={<PaymentIcon />}
                >
                  I have made the payment
                </Button>
              </CardContent>
            </Card>
          )}

          {activeStep === 1 && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Step 2: Payment Confirmation
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Please confirm that you have made the payment of ₹{loan.processingFee}
                </Typography>

                <Alert severity="warning" sx={{ mb: 3 }}>
                  <Typography variant="body2">
                    Make sure you have completed the payment before proceeding to the next step.
                    You will need the UTR number and a screenshot of the payment.
                  </Typography>
                </Alert>

                <Box display="flex" gap={2}>
                  <Button onClick={handleBack}>
                    Back
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleNext}
                    fullWidth
                  >
                    Yes, I have made the payment
                  </Button>
                </Box>
              </CardContent>
            </Card>
          )}

          {activeStep === 2 && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Step 3: Upload Payment Proof
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Please provide the UTR number and upload a screenshot of your payment
                </Typography>

                <Box component="form" onSubmit={handleSubmitPayment}>
                  <TextField
                    fullWidth
                    label="UTR Number"
                    value={utrNumber}
                    onChange={(e) => setUtrNumber(e.target.value)}
                    required
                    sx={{ mb: 3 }}
                    helperText="Enter the UTR number from your payment confirmation"
                  />

                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Payment Screenshot *
                    </Typography>
                    <Button
                      component="label"
                      variant="outlined"
                      startIcon={<CloudUpload />}
                      fullWidth
                      sx={{ mb: 1 }}
                    >
                      Choose Screenshot
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleFileChange}
                        hidden
                        required
                      />
                    </Button>
                    {screenshot && (
                      <Typography variant="body2" color="text.secondary">
                        {screenshot.name}
                      </Typography>
                    )}
                  </Box>

                  <Box display="flex" gap={2}>
                    <Button onClick={handleBack}>
                      Back
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={loading}
                      fullWidth
                    >
                      {loading ? 'Submitting...' : 'Submit Payment Proof'}
                    </Button>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>
    </Layout>
  )
}

export default Payment
