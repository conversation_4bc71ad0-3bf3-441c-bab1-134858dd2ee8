import React, { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Alert,
  LinearProgress,
  Chip,
  Grid,
  Button
} from '@mui/material'
import {
  CheckCircle,
  Schedule,
  Error,
  Refresh
} from '@mui/icons-material'
import { useParams, useNavigate } from 'react-router-dom'
import Layout from '../components/Layout'
import axios from 'axios'

const PaymentStatus = () => {
  const { loanId } = useParams()
  const navigate = useNavigate()
  const [payment, setPayment] = useState(null)
  const [waitingStatus, setWaitingStatus] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchPaymentStatus()
    const interval = setInterval(fetchWaitingStatus, 60000) // Check every minute
    
    return () => clearInterval(interval)
  }, [loanId])

  const fetchPaymentStatus = async () => {
    try {
      const response = await axios.get(`/api/payments/loan/${loanId}`)
      setPayment(response.data.payment)
      await fetchWaitingStatus()
    } catch (error) {
      setError('Failed to fetch payment status')
    } finally {
      setLoading(false)
    }
  }

  const fetchWaitingStatus = async () => {
    try {
      const response = await axios.get(`/api/payments/waiting-status/${loanId}`)
      setWaitingStatus(response.data)
    } catch (error) {
      console.error('Failed to fetch waiting status:', error)
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'verified':
        return <CheckCircle color="success" />
      case 'pending':
        return <Schedule color="warning" />
      case 'rejected':
        return <Error color="error" />
      default:
        return <Schedule color="warning" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'verified':
        return 'success'
      case 'pending':
        return 'warning'
      case 'rejected':
        return 'error'
      default:
        return 'default'
    }
  }

  const formatTime = (minutes) => {
    if (minutes <= 0) return '0 minutes'
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    
    if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ${mins} minute${mins !== 1 ? 's' : ''}`
    }
    return `${mins} minute${mins !== 1 ? 's' : ''}`
  }

  if (loading) {
    return (
      <Layout title="Payment Status">
        <Typography>Loading...</Typography>
      </Layout>
    )
  }

  if (error) {
    return (
      <Layout title="Payment Status">
        <Alert severity="error">{error}</Alert>
      </Layout>
    )
  }

  return (
    <Layout title="Payment Status">
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                {getStatusIcon(payment.status)}
                <Typography variant="h5" sx={{ ml: 2 }}>
                  Payment Status
                </Typography>
              </Box>
              
              <Chip
                label={payment.status.toUpperCase()}
                color={getStatusColor(payment.status)}
                size="large"
                sx={{ mb: 2 }}
              />

              <Typography variant="body1" paragraph>
                UTR Number: <strong>{payment.utrNumber}</strong>
              </Typography>
              
              <Typography variant="body2" color="text.secondary" paragraph>
                Payment Amount: ₹{payment.amount.toLocaleString()}
              </Typography>
              
              <Typography variant="body2" color="text.secondary" paragraph>
                Submitted: {new Date(payment.submittedAt).toLocaleString()}
              </Typography>

              {payment.verifiedAt && (
                <Typography variant="body2" color="text.secondary" paragraph>
                  Verified: {new Date(payment.verifiedAt).toLocaleString()}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {payment.status === 'pending' && waitingStatus && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Waiting Period
                </Typography>
                
                {!waitingStatus.isWaitingPeriodOver ? (
                  <>
                    <Typography variant="body1" paragraph>
                      Your payment is being processed. Please wait for verification.
                    </Typography>
                    
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Time remaining: {formatTime(waitingStatus.remainingMinutes)}
                      </Typography>
                      <LinearProgress 
                        variant="determinate" 
                        value={((30 - waitingStatus.remainingMinutes) / 30) * 100}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>

                    <Alert severity="info">
                      <Typography variant="body2">
                        The verification process typically takes up to 30 minutes. 
                        You will be notified once your payment is verified.
                      </Typography>
                    </Alert>
                  </>
                ) : (
                  <Alert severity="warning">
                    <Typography variant="body2">
                      The 30-minute waiting period has ended. If your payment hasn't been verified yet, 
                      please contact support or try refreshing the status.
                    </Typography>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Grid>
        )}

        {payment.status === 'verified' && (
          <Grid item xs={12}>
            <Alert severity="success">
              <Typography variant="body1">
                <strong>Payment Verified Successfully!</strong>
              </Typography>
              <Typography variant="body2">
                Your payment has been verified and your loan application is now being processed. 
                You will be notified about the loan approval status soon.
              </Typography>
            </Alert>
          </Grid>
        )}

        {payment.status === 'rejected' && (
          <Grid item xs={12}>
            <Alert severity="error">
              <Typography variant="body1">
                <strong>Payment Verification Failed</strong>
              </Typography>
              <Typography variant="body2">
                There was an issue with your payment verification. Please contact support 
                or submit a new payment proof.
              </Typography>
            </Alert>
          </Grid>
        )}

        <Grid item xs={12}>
          <Box display="flex" gap={2}>
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={fetchPaymentStatus}
            >
              Refresh Status
            </Button>
            
            <Button
              variant="contained"
              onClick={() => navigate('/dashboard')}
            >
              Back to Dashboard
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Layout>
  )
}

export default PaymentStatus
